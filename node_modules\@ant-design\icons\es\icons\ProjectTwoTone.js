import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import ProjectTwoToneSvg from "@ant-design/icons-svg/es/asn/ProjectTwoTone";
import AntdIcon from "../components/AntdIcon";
var ProjectTwoTone = function ProjectTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: ProjectTwoToneSvg
  }));
};

/**![project](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg4MCAxMTJIMTQ0Yy0xNy43IDAtMzIgMTQuMy0zMiAzMnY3MzZjMCAxNy43IDE0LjMgMzIgMzIgMzJoNzM2YzE3LjcgMCAzMi0xNC4zIDMyLTMyVjE0NGMwLTE3LjctMTQuMy0zMi0zMi0zMnptLTQwIDcyOEgxODRWMTg0aDY1NnY2NTZ6IiBmaWxsPSIjMTY3N2ZmIiAvPjxwYXRoIGQ9Ik0xODQgODQwaDY1NlYxODRIMTg0djY1NnptNDcyLTU2MGMwLTQuNCAzLjYtOCA4LThoODBjNC40IDAgOCAzLjYgOCA4djI1NmMwIDQuNC0zLjYgOC04IDhoLTgwYy00LjQgMC04LTMuNi04LThWMjgwem0tMTkyIDBjMC00LjQgMy42LTggOC04aDgwYzQuNCAwIDggMy42IDggOHYxODRjMCA0LjQtMy42IDgtOCA4aC04MGMtNC40IDAtOC0zLjYtOC04VjI4MHptLTE5MiAwYzAtNC40IDMuNi04IDgtOGg4MGM0LjQgMCA4IDMuNiA4IDh2NDY0YzAgNC40LTMuNiA4LTggOGgtODBjLTQuNCAwLTgtMy42LTgtOFYyODB6IiBmaWxsPSIjZTZmNGZmIiAvPjxwYXRoIGQ9Ik0yODAgNzUyaDgwYzQuNCAwIDgtMy42IDgtOFYyODBjMC00LjQtMy42LTgtOC04aC04MGMtNC40IDAtOCAzLjYtOCA4djQ2NGMwIDQuNCAzLjYgOCA4IDh6bTE5Mi0yODBoODBjNC40IDAgOC0zLjYgOC04VjI4MGMwLTQuNC0zLjYtOC04LThoLTgwYy00LjQgMC04IDMuNi04IDh2MTg0YzAgNC40IDMuNiA4IDggOHptMTkyIDcyaDgwYzQuNCAwIDgtMy42IDgtOFYyODBjMC00LjQtMy42LTgtOC04aC04MGMtNC40IDAtOCAzLjYtOCA4djI1NmMwIDQuNCAzLjYgOCA4IDh6IiBmaWxsPSIjMTY3N2ZmIiAvPjwvc3ZnPg==) */
var RefIcon = /*#__PURE__*/React.forwardRef(ProjectTwoTone);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'ProjectTwoTone';
}
export default RefIcon;