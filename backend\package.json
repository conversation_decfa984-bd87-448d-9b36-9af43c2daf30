{"name": "backend", "version": "1.0.0", "description": "", "type": "module", "main": "dist/index.js", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "start": "node dist/index.js"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"bcrypt": "^6.0.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "express-async-handler": "^1.2.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.15.1", "resend": "^4.7.0", "sharp": "^0.34.2", "slugify": "^1.6.6"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/jsonwebtoken": "^9.0.9", "@types/mongoose": "^5.11.96", "@types/node": "^24.0.1", "tsx": "^4.7.0", "typescript": "^5.8.3"}}