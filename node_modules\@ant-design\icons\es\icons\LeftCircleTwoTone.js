import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import LeftCircleTwoToneSvg from "@ant-design/icons-svg/es/asn/LeftCircleTwoTone";
import AntdIcon from "../components/AntdIcon";
var LeftCircleTwoTone = function LeftCircleTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: LeftCircleTwoToneSvg
  }));
};

/**![left-circle](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTUxMiAxNDBjLTIwNS40IDAtMzcyIDE2Ni42LTM3MiAzNzJzMTY2LjYgMzcyIDM3MiAzNzIgMzcyLTE2Ni42IDM3Mi0zNzItMTY2LjYtMzcyLTM3Mi0zNzJ6bTEwNCAyNDAuOWMwIDEwLjMtNC45IDE5LjktMTMuMiAyNS45TDQ1Ny40IDUxMmwxNDUuNCAxMDUuMWM4LjMgNiAxMy4yIDE1LjcgMTMuMiAyNS45djQ2LjljMCA2LjUtNy40IDEwLjMtMTIuNyA2LjVsLTI0Ni0xNzhhNy45NSA3Ljk1IDAgMDEwLTEyLjlsMjQ2LTE3OGM1LjMtMy44IDEyLjcgMCAxMi43IDYuNXY0Ni45eiIgZmlsbD0iI2U2ZjRmZiIgLz48cGF0aCBkPSJNNTEyIDY0QzI2NC42IDY0IDY0IDI2NC42IDY0IDUxMnMyMDAuNiA0NDggNDQ4IDQ0OCA0NDgtMjAwLjYgNDQ4LTQ0OFM3NTkuNCA2NCA1MTIgNjR6bTAgODIwYy0yMDUuNCAwLTM3Mi0xNjYuNi0zNzItMzcyczE2Ni42LTM3MiAzNzItMzcyIDM3MiAxNjYuNiAzNzIgMzcyLTE2Ni42IDM3Mi0zNzIgMzcyeiIgZmlsbD0iIzE2NzdmZiIgLz48cGF0aCBkPSJNNjAzLjMgMzI3LjVsLTI0NiAxNzhhNy45NSA3Ljk1IDAgMDAwIDEyLjlsMjQ2IDE3OGM1LjMgMy44IDEyLjcgMCAxMi43LTYuNVY2NDNjMC0xMC4yLTQuOS0xOS45LTEzLjItMjUuOUw0NTcuNCA1MTJsMTQ1LjQtMTA1LjJjOC4zLTYgMTMuMi0xNS42IDEzLjItMjUuOVYzMzRjMC02LjUtNy40LTEwLjMtMTIuNy02LjV6IiBmaWxsPSIjMTY3N2ZmIiAvPjwvc3ZnPg==) */
var RefIcon = /*#__PURE__*/React.forwardRef(LeftCircleTwoTone);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'LeftCircleTwoTone';
}
export default RefIcon;