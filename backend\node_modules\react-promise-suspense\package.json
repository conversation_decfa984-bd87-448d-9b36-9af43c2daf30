{"name": "react-promise-suspense", "version": "0.3.4", "description": "React hook for resolving promises with Suspense support", "main": "build/index.js", "repository": {"type": "git", "url": "git+https://github.com/vigzmv/react-promise-suspense.git"}, "keywords": ["react", "fetch", "suspense", "promise", "hooks"], "author": "<PERSON><PERSON><PERSON> <<EMAIL>", "license": "MIT", "bugs": {"url": "https://github.com/vigzmv/react-promise-suspense/issues"}, "homepage": "https://github.com/vigzmv/react-promise-suspense#readme", "scripts": {"build": "tsc --outDir build", "dev": "tsc --outDir build --watch", "prepublishOnly": "npm run build", "test": "exit 0"}, "devDependencies": {"@types/node": "^18.13.0", "typescript": "^4.9.5"}, "dependencies": {"fast-deep-equal": "^2.0.1"}}