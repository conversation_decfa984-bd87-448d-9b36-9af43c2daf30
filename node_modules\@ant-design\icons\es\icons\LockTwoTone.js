import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import LockTwoToneSvg from "@ant-design/icons-svg/es/asn/LockTwoTone";
import AntdIcon from "../components/AntdIcon";
var LockTwoTone = function LockTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: LockTwoToneSvg
  }));
};

/**![lock](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTgzMiA0NjRoLTY4VjI0MGMwLTcwLjctNTcuMy0xMjgtMTI4LTEyOEgzODhjLTcwLjcgMC0xMjggNTcuMy0xMjggMTI4djIyNGgtNjhjLTE3LjcgMC0zMiAxNC4zLTMyIDMydjM4NGMwIDE3LjcgMTQuMyAzMiAzMiAzMmg2NDBjMTcuNyAwIDMyLTE0LjMgMzItMzJWNDk2YzAtMTcuNy0xNC4zLTMyLTMyLTMyek0zMzIgMjQwYzAtMzAuOSAyNS4xLTU2IDU2LTU2aDI0OGMzMC45IDAgNTYgMjUuMSA1NiA1NnYyMjRIMzMyVjI0MHptNDYwIDYwMEgyMzJWNTM2aDU2MHYzMDR6IiBmaWxsPSIjMTY3N2ZmIiAvPjxwYXRoIGQ9Ik0yMzIgODQwaDU2MFY1MzZIMjMydjMwNHptMjgwLTIyNmE0OC4wMSA0OC4wMSAwIDAxMjggODd2NTNjMCA0LjQtMy42IDgtOCA4aC00MGMtNC40IDAtOC0zLjYtOC04di01M2E0OC4wMSA0OC4wMSAwIDAxMjgtODd6IiBmaWxsPSIjZTZmNGZmIiAvPjxwYXRoIGQ9Ik00ODQgNzAxdjUzYzAgNC40IDMuNiA4IDggOGg0MGM0LjQgMCA4LTMuNiA4LTh2LTUzYTQ4LjAxIDQ4LjAxIDAgMTAtNTYgMHoiIGZpbGw9IiMxNjc3ZmYiIC8+PC9zdmc+) */
var RefIcon = /*#__PURE__*/React.forwardRef(LockTwoTone);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'LockTwoTone';
}
export default RefIcon;