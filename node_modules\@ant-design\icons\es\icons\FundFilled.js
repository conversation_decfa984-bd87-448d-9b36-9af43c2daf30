import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import FundFilledSvg from "@ant-design/icons-svg/es/asn/FundFilled";
import AntdIcon from "../components/AntdIcon";
var FundFilled = function FundFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: FundFilledSvg
  }));
};

/**![fund](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTkyNiAxNjRIOTRjLTE3LjcgMC0zMiAxNC4zLTMyIDMydjY0MGMwIDE3LjcgMTQuMyAzMiAzMiAzMmg4MzJjMTcuNyAwIDMyLTE0LjMgMzItMzJWMTk2YzAtMTcuNy0xNC4zLTMyLTMyLTMyem0tOTIuMyAxOTQuNGwtMjk3IDI5Ny4yYTguMDMgOC4wMyAwIDAxLTExLjMgMEw0MTAuOSA1NDEuMSAyMzguNCA3MTMuN2E4LjAzIDguMDMgMCAwMS0xMS4zIDBsLTM2LjgtMzYuOGE4LjAzIDguMDMgMCAwMTAtMTEuM2wyMTQuOS0yMTVjMy4xLTMuMSA4LjItMy4xIDExLjMgMEw1MzEgNTY1bDI1NC41LTI1NC42YzMuMS0zLjEgOC4yLTMuMSAxMS4zIDBsMzYuOCAzNi44YzMuMiAzIDMuMiA4LjEuMSAxMS4yeiIgLz48L3N2Zz4=) */
var RefIcon = /*#__PURE__*/React.forwardRef(FundFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'FundFilled';
}
export default RefIcon;