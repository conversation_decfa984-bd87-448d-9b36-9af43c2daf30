import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![rotate-left](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGRlZnM+PHN0eWxlIC8+PC9kZWZzPjxwYXRoIGQ9Ik02NzIgNDE4SDE0NGMtMTcuNyAwLTMyIDE0LjMtMzIgMzJ2NDE0YzAgMTcuNyAxNC4zIDMyIDMyIDMyaDUyOGMxNy43IDAgMzItMTQuMyAzMi0zMlY0NTBjMC0xNy43LTE0LjMtMzItMzItMzJ6bS00NCA0MDJIMTg4VjQ5NGg0NDB2MzI2eiIgLz48cGF0aCBkPSJNODE5LjMgMzI4LjVjLTc4LjgtMTAwLjctMTk2LTE1My42LTMxNC42LTE1NC4ybC0uMi02NGMwLTYuNS03LjYtMTAuMS0xMi42LTYuMWwtMTI4IDEwMWMtNCAzLjEtMy45IDkuMSAwIDEyLjNMNDkyIDMxOC42YzUuMSA0IDEyLjcuNCAxMi42LTYuMXYtNjMuOWMxMi45LjEgMjUuOS45IDM4LjggMi41IDQyLjEgNS4yIDgyLjEgMTguMiAxMTkgMzguNyAzOC4xIDIxLjIgNzEuMiA0OS43IDk4LjQgODQuMyAyNy4xIDM0LjcgNDYuNyA3My43IDU4LjEgMTE1LjhhMzI1Ljk1IDMyNS45NSAwIDAxNi41IDE0MC45aDc0LjljMTQuOC0xMDMuNi0xMS4zLTIxMy04MS0zMDIuM3oiIC8+PC9zdmc+) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
