import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import HeartFilledSvg from "@ant-design/icons-svg/es/asn/HeartFilled";
import AntdIcon from "../components/AntdIcon";
var HeartFilled = function HeartFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: HeartFilledSvg
  }));
};

/**![heart](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTkyMyAyODMuNmEyNjAuMDQgMjYwLjA0IDAgMDAtNTYuOS04Mi44IDI2NC40IDI2NC40IDAgMDAtODQtNTUuNUEyNjUuMzQgMjY1LjM0IDAgMDA2NzkuNyAxMjVjLTQ5LjMgMC05Ny40IDEzLjUtMTM5LjIgMzktMTAgNi4xLTE5LjUgMTIuOC0yOC41IDIwLjEtOS03LjMtMTguNS0xNC0yOC41LTIwLjEtNDEuOC0yNS41LTg5LjktMzktMTM5LjItMzktMzUuNSAwLTY5LjkgNi44LTEwMi40IDIwLjMtMzEuNCAxMy01OS43IDMxLjctODQgNTUuNWEyNTguNDQgMjU4LjQ0IDAgMDAtNTYuOSA4Mi44Yy0xMy45IDMyLjMtMjEgNjYuNi0yMSAxMDEuOSAwIDMzLjMgNi44IDY4IDIwLjMgMTAzLjMgMTEuMyAyOS41IDI3LjUgNjAuMSA0OC4yIDkxIDMyLjggNDguOSA3Ny45IDk5LjkgMTMzLjkgMTUxLjYgOTIuOCA4NS43IDE4NC43IDE0NC45IDE4OC42IDE0Ny4zbDIzLjcgMTUuMmMxMC41IDYuNyAyNCA2LjcgMzQuNSAwbDIzLjctMTUuMmMzLjktMi41IDk1LjctNjEuNiAxODguNi0xNDcuMyA1Ni01MS43IDEwMS4xLTEwMi43IDEzMy45LTE1MS42IDIwLjctMzAuOSAzNy02MS41IDQ4LjItOTEgMTMuNS0zNS4zIDIwLjMtNzAgMjAuMy0xMDMuMy4xLTM1LjMtNy02OS42LTIwLjktMTAxLjl6IiAvPjwvc3ZnPg==) */
var RefIcon = /*#__PURE__*/React.forwardRef(HeartFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'HeartFilled';
}
export default RefIcon;