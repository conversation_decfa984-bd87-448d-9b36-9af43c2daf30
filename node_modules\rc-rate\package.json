{"name": "rc-rate", "version": "2.13.1", "description": "React Star Rate Component", "engines": {"node": ">=8.x"}, "keywords": ["react", "react-component", "react-rate", "rate"], "homepage": "https://github.com/react-component/rate", "repository": {"type": "git", "url": "https://github.com/react-component/rate.git"}, "bugs": {"url": "https://github.com/react-component/rate/issues"}, "files": ["lib", "es", "assets/*.css"], "license": "MIT", "main": "./lib/index", "module": "./es/index", "scripts": {"start": "dumi dev", "docs:build": "dumi build", "docs:deploy": "gh-pages -d .doc", "compile": "father build && lessc assets/index.less assets/index.css", "prepare": "dumi setup", "prepublishOnly": "npm run compile && np --yolo --no-publish", "postpublish": "npm run docs:build && npm run docs:deploy", "lint": "eslint src/ --ext .ts,.tsx,.jsx,.js,.md", "prettier": "prettier --write \"**/*.{ts,tsx,js,jsx,json,md}\"", "test": "rc-test", "coverage": "rc-test --coverage", "now-build": "npm run docs:build"}, "dependencies": {"@babel/runtime": "^7.10.1", "classnames": "^2.2.5", "rc-util": "^5.0.1"}, "devDependencies": {"@rc-component/father-plugin": "^1.0.0", "@types/classnames": "^2.2.9", "@types/jest": "^29.5.1", "@types/react": "^17.0.15", "@types/react-dom": "^17.0.9", "@umijs/fabric": "^3.0.0", "cheerio": "1.0.0-rc.12", "cross-env": "^7.0.0", "dumi": "^2.1.2", "enzyme": "^3.1.1", "enzyme-adapter-react-16": "^1.15.6", "enzyme-to-json": "^3.1.2", "eslint": "^7.1.0", "father": "^4.0.0", "gh-pages": "^3.1.0", "less": "^3.0.0", "np": "^7.0.0", "rc-test": "^7.0.15", "rc-tooltip": "^5.0.1", "react": "^16.0.0", "react-dom": "^16.0.0", "typescript": "^5.0.4"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}}