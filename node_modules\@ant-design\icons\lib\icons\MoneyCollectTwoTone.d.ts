import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![money-collect](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTI1NiA3NDQuNGwyNTYgOTMuMSAyNTYtOTMuMVYxODRIMjU2djU2MC40ek0zNTkuNyAzMTNjMS4yLS43IDIuNS0xIDMuOC0xaDU1LjdhOCA4IDAgMDE3LjEgNC40TDUxMSA0ODUuMmgzLjNMNTk5IDMxNi40YzEuMy0yLjcgNC4xLTQuNCA3LjEtNC40aDU0LjVjNC40IDAgOCAzLjYgOC4xIDcuOSAwIDEuMy0uNCAyLjYtMSAzLjhMNTY0IDUxNS4zaDU3LjZjNC40IDAgOCAzLjYgOCA4djI3LjFjMCA0LjQtMy42IDgtOCA4aC03Ni4zdjM5aDc2LjNjNC40IDAgOCAzLjYgOCA4djI3LjFjMCA0LjQtMy42IDgtOCA4aC03Ni4zVjcwNGMwIDQuNC0zLjYgOC04IDhoLTQ5LjljLTQuNCAwLTgtMy42LTgtOHYtNjMuNGgtNzZjLTQuNCAwLTgtMy42LTgtOHYtMjcuMWMwLTQuNCAzLjYtOCA4LThoNzZ2LTM5aC03NmMtNC40IDAtOC0zLjYtOC04di0yNy4xYzAtNC40IDMuNi04IDgtOGg1N0wzNTYuNSAzMjMuOGMtMi4xLTMuOC0uNy04LjcgMy4yLTEwLjh6IiBmaWxsPSIjZTZmNGZmIiAvPjxwYXRoIGQ9Ik05MTEuNSA3MDAuN2E4IDggMCAwMC0xMC4zLTQuOEw4NDAgNzE4LjJWMTgwYzAtMzcuNi0zMC40LTY4LTY4LTY4SDI1MmMtMzcuNiAwLTY4IDMwLjQtNjggNjh2NTM4LjJsLTYxLjMtMjIuM2MtLjktLjMtMS44LS41LTIuNy0uNS00LjQgMC04IDMuNi04IDhWNzYzYzAgMy4zIDIuMSA2LjMgNS4zIDcuNUw1MDEgOTEwLjFjNy4xIDIuNiAxNC44IDIuNiAyMS45IDBsMzgzLjgtMTM5LjVjMy4yLTEuMiA1LjMtNC4yIDUuMy03LjV2LTU5LjZjMC0xLS4yLTEuOS0uNS0yLjh6TTc2OCA3NDQuNGwtMjU2IDkzLjEtMjU2LTkzLjFWMTg0aDUxMnY1NjAuNHoiIGZpbGw9IiMxNjc3ZmYiIC8+PHBhdGggZD0iTTQ2MC40IDUxNS40aC01N2MtNC40IDAtOCAzLjYtOCA4djI3LjFjMCA0LjQgMy42IDggOCA4aDc2djM5aC03NmMtNC40IDAtOCAzLjYtOCA4djI3LjFjMCA0LjQgMy42IDggOCA4aDc2VjcwNGMwIDQuNCAzLjYgOCA4IDhoNDkuOWM0LjQgMCA4LTMuNiA4LTh2LTYzLjVoNzYuM2M0LjQgMCA4LTMuNiA4LTh2LTI3LjFjMC00LjQtMy42LTgtOC04aC03Ni4zdi0zOWg3Ni4zYzQuNCAwIDgtMy42IDgtOHYtMjcuMWMwLTQuNC0zLjYtOC04LThINTY0bDEwMy43LTE5MS42Yy42LTEuMiAxLTIuNSAxLTMuOC0uMS00LjMtMy43LTcuOS04LjEtNy45aC01NC41Yy0zIDAtNS44IDEuNy03LjEgNC40bC04NC43IDE2OC44SDUxMWwtODQuNy0xNjguOGE4IDggMCAwMC03LjEtNC40aC01NS43Yy0xLjMgMC0yLjYuMy0zLjggMS0zLjkgMi4xLTUuMyA3LTMuMiAxMC44bDEwMy45IDE5MS42eiIgZmlsbD0iIzE2NzdmZiIgLz48L3N2Zz4=) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
