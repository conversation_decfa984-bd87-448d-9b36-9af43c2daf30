{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@popperjs/core": "^2.11.8", "antd": "^5.26.6", "axios": "^1.9.0", "bootstrap": "^5.3.6", "lucide-react": "^0.511.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-icons": "^5.5.0", "react-router-dom": "^7.6.2", "react-toastify": "^11.0.5", "sass": "^1.89.1"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/axios": "^0.9.36", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@types/react-toastify": "^4.0.2", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5"}}