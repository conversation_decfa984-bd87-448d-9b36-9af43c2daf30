import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import MailFilledSvg from "@ant-design/icons-svg/es/asn/MailFilled";
import AntdIcon from "../components/AntdIcon";
var MailFilled = function MailFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: MailFilledSvg
  }));
};

/**![mail](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTkyOCAxNjBIOTZjLTE3LjcgMC0zMiAxNC4zLTMyIDMydjY0MGMwIDE3LjcgMTQuMyAzMiAzMiAzMmg4MzJjMTcuNyAwIDMyLTE0LjMgMzItMzJWMTkyYzAtMTcuNy0xNC4zLTMyLTMyLTMyem0tODAuOCAxMDguOUw1MzEuNyA1MTQuNGMtNy44IDYuMS0xOC43IDYuMS0yNi41IDBMMTg5LjYgMjY4LjlBNy4yIDcuMiAwIDAxMTk0IDI1Nmg2NDguOGE3LjIgNy4yIDAgMDE0LjQgMTIuOXoiIC8+PC9zdmc+) */
var RefIcon = /*#__PURE__*/React.forwardRef(MailFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'MailFilled';
}
export default RefIcon;